# $Id: <PERSON><PERSON>,v 1.1.1.1 2006/09/14 01:59:09 root Exp $ # #	GENERIC configuration for Galileo EV64240 # #  This file is supposed to be included by target file after #  endian has been defined.
#
machine		soc		loongarch	ls2k300		# CPU Architecture, Platform, CPU name

include "conf/LOONGSON_PUBILC"

option		LOONGSON_2K
option		LOONGARCH_2K300
option		LOONGSON_2K300
option		VRAM_SIZE=16
option		SOC_CPU

option		BOOT_PARAMS_BPI				# for 4.19 kernel
#option		BOOT_PARAMS_EFI				# for older kernel

#
# System str options
#
#option     LS_STR
#
# Platform options
#
option		LS3_HT              # Enable the IO cache coherent for ls2k
option      loongson3A3
option      LSMC_2
#option     ARB_LEVEL
#option     DDR3_DIMM
option		TOT_NODE_NUM=1

#option      QUICK_START

#<<<<<<<<<<<<<<
###these options can NOT defined at the same time
#option		DDR3_SODIMM #1Rx8 2GB
#option		DDR3_SMT    #for SMT x16 SDRAM 4 chip 2GB
#>>>>>>>>>>>>>>

#option		AUTO_DDR_CONFIG				# for DIMM recognize
option		CORE_FREQ=1000				# Must be several times 125
option		DDR_FREQ=800
option		HIGH_MEM_WIN_BASE_ADDR=0x80000000
#option		DDR_RESET_REVERT
option		USE_DDR_1TMODE
option		MC_PER_NODE=1

select		mod_txt

select		cmd_dtb
option		DTB

#
# Platform Trust
#
select      cmd_tcm2
option      TCM2
#option      LS2_SE
#option      TCM_ENV_MEASUREMENT

#
# Platform options
#
select		mod_display
select		ext2
select		mod_x86emu_int10
option		MY61IO
option      VGA_BASE=0xb0000000
select		mod_vgacon
#select		sis315e
#option		SIS315E
option		VGA_NO_ROM				# display card has no rom
#select		mod_sisfb

select		mod_framebuffer
select		mod_vesa
option		CONFIG_VIDEO_16BPP
#option		X1280x1024
#option		X640x480
#option		X800x600
#option		X1024x768
#option		X800x480
#option		CONFIG_VIDEO_8BPP_INDEX
### config for st7701s lcd
#select		st7701s
option		FB_XSIZE=1024
option		FB_YSIZE=600
option		LCD_EN
#option		CONFIG_VIDEO_32BPP

#
# HAVE options. What tgt level provide
#
option		INTERNAL_RTC				# chip internal RTC
#option		EXTERNAL_RTC				# external RTC
option		HPET_RTC
rmoption    CONFIG_VIDEO_LOGO
rmoption    PROGRESS_BAR
#option     VESAFB
option		CONFIG_SLOW_PCI_FOR_BROKENDEV
option		PCIVERBOSE=5
option		PCI_INT_GPIO=86

#
#  Now the Machine specification
#
mainbus0    at root
localbus0	at mainbus0
#fd0		at mainbus0
pcibr*		at mainbus0
#pcibr1		at mainbus0
pci*		at pcibr?
ppb*		at pci? dev ? function ?		# PCI-PCI bridges
pci*		at ppb? bus ?

#lahci0		at localbus0 base 0x800000001f040000	# AHCI
#ahci_sd*	at lahci0
#ahci_cdrom*	at lahci0

#ahci*		at pci? dev ? function ?
#ahci_sd*	at ahci?
#ahci_cdrom*	at ahci?

#ohci*		at pci? dev ? function ?		# OHCI
#usb*		at ohci?
#lehci*		at pci? dev ? function ?		# EHCI
#usb*		at lehci?

######## todo ?????????????????
#lohci1		at localbus0 base 0x8000000016080000	# EHCI as OHCI
#usb*		at lohci1

lohci0		at localbus0 base 0x8000000016088000	# OHCI
usb*		at lohci0


lotg0		at localbus0 base 0x8000000016040000
usb* 		at lotg0
#lxhci0		at localbus0 base 0x800000001f060000	# XHCI
#xhci0		at lxhci0
#usb*		at xhci0
#select		mod_usb_xhci
#select		otg-device

##### USB
#uhci*		at pci? dev ? function ?
option		TEST_USB_HOST
#option		CONFIG_USB_HOTPLUG
option		USB3_USE_INTER_CLK

#### Networking Devices
syn0		at localbus0 base 0x8000000016020000
syn1		at localbus0 base 0x8000000016030000
#pcisyn0	at pci? dev ? function ?
#pcisyn1	at pci? dev ? function ?
select		gmac
option		CLOSE_GMAC_RX_DELAY
option		USE_GMAC_NUM=2
option		GMAC_USE_FLASH_MAC

igb*		at pci? dev ? function ?		# Intel 82576
select      igb1

#### SCSI support
#siop*		at pci? dev ? function ?		# Symbios/NCR 53c...
#scsibus*	at siop?
#sd*		at scsibus? target ? lun ?
#cd*		at scsibus? target ? lun ?

#### Networking Devices
#gt0		at localbus? base 4
#gt1		at localbus? base 5
#gt2		at localbus? base 6

# fxp normally only used for debugging (enable/disable both)
#fxp*		at pci? dev ? function ?		# Intel 82559 Device
#inphy*		at mii? phy ?				# Intel 82555 PHYs
rtl*		at pci? dev ? function ?
#rtk*		at pci? dev ? function ?
em*		    at pci? dev ? function ?
#uhci*		at pci? dev ? function ?
#ohci*		at pci? dev ? function ?
#usb*		at usbbus ?
#ohci1		at pci? dev ? function ?

#select		mod_usb_ehci
select      mod_usb_otg
#select		pcinvme
#pcinvme*	at pci? dev ? function ?
#nvme*		at pcinvme?

#### IDE controllers
option		IDE_DMA
pciide*		at pci ? dev ? function ? flags 0x0000

#### IDE hard drives
wd*		    at pciide? channel ? drive ? flags 0x0000

#option MCI_DEBUG
loopdev0	at mainbus0
select      mci
option      SDIO0_EN=1
emmc0       at localbus0 flags 0
option      SDIO1_EN=1
tfcard0     at localbus0 flags 0   

option AIC8800_WIFI


#### Pseudo devices
pseudo-device	loop	1				# network loopback

ide_cd* 	at pciide? channel ? drive ? flags 0x0001

select		cmd_testfire
#option		USE_ENVMAC
option		FLOATINGPT
#option		WDC_NORESET
option		KBD_CHECK_FAST
option      NM25P80

select		nand
option 		USE_I2C_MASTER
#option		BEEP_ON
select		m25p80
select		pai_2k300_dual_net
#select		pai_99
#select		pai_99_plus
#select		pai_99_plus_single_net
option		USE_120M
#option		USE_UART5_DEBUG
option		USE_UART6_DEBUG     #pai_2k300_dual_net use
#option		USE_UART0_DEBUG	    #pai_99 & pai_99_PLUS use
